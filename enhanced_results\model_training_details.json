{"Logistic Regression": {"training_time": 0.023258, "prediction_time": 0.002, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Random Forest": {"training_time": 1.095306, "prediction_time": 0.089551, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.9382911392405063, "has_feature_importance": true}, "Gradient Boosting": {"training_time": 1.634226, "prediction_time": 0.002742, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.0008251144813036027, "has_feature_importance": true}, "XGBoost": {"training_time": 0.769097, "prediction_time": 0.016485, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "SVM (RBF)": {"training_time": 0.11106, "prediction_time": 0.007999, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "k‑NN": {"training_time": 0.0, "prediction_time": 0.022407, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Enhanced MLP": {"training_time": 0.297076, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "LightGBM": {"training_time": 0.425659, "prediction_time": 0.015628, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "CatBoost": {"training_time": 2.383304, "prediction_time": 0.015645, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "Gaussian Process": {"training_time": 33.528344, "prediction_time": 0.026892, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Extra Trees": {"training_time": 1.132477, "prediction_time": 0.092309, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "oob_score": 0.9430379746835443, "has_feature_importance": true}, "Stacking Classifier": {"training_time": 2.665973, "prediction_time": 0.053077, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Voting Classifier (Hard)": {"training_time": 1.131326, "prediction_time": 0.087038, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Voting Classifier (Soft)": {"training_time": 0.45916, "prediction_time": 0.044384, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159}, "Tuned XGBoost": {"training_time": 0.249785, "prediction_time": 0.003567, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}, "Tuned LightGBM": {"training_time": 0.146343, "prediction_time": 0.0, "n_features": 18, "n_samples_train": 632, "n_samples_test": 159, "has_feature_importance": true}}