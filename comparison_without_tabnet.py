import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import (accuracy_score, precision_score, recall_score,
                             f1_score, roc_auc_score, confusion_matrix, RocCurveDisplay)
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, StackingClassifier
from sklearn.svm import SVC
from sklearn.neighbors import KNeighborsClassifier
from sklearn.neural_network import MLPClassifier
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
from sklearn.gaussian_process import GaussianProcessClassifier
from sklearn.gaussian_process.kernels import RBF

RANDOM_STATE = 42
TEST_SIZE = 0.20

# ---------------------------------------------------------------------
# 1. Load data
# ---------------------------------------------------------------------

df = pd.read_csv("data_diag.csv")
X = df.drop(columns=["Diagnosis"])
y = df["Diagnosis"]

# Stratified split to keep class ratios
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=TEST_SIZE, stratify=y, random_state=RANDOM_STATE)

# ---------------------------------------------------------------------
# 2. Define models
# ---------------------------------------------------------------------

models = {
    "Logistic Regression": Pipeline([
        ("scaler", StandardScaler()),
        ("clf", LogisticRegression(max_iter=1000, multi_class="multinomial", random_state=RANDOM_STATE))
    ]),
    "Random Forest": RandomForestClassifier(n_estimators=300, random_state=RANDOM_STATE),
    "Gradient Boosting": GradientBoostingClassifier(random_state=RANDOM_STATE),
    "XGBoost": xgb.XGBClassifier(
        n_estimators=300,
        learning_rate=0.1,
        max_depth=5,
        random_state=RANDOM_STATE
    ),
    "SVM (RBF)": Pipeline([
        ("scaler", StandardScaler()),
        ("clf", SVC(kernel="rbf", probability=True, random_state=RANDOM_STATE))
    ]),
    "k‑NN": Pipeline([
        ("scaler", StandardScaler()),
        ("clf", KNeighborsClassifier(n_neighbors=5))
    ]),
    "MLP (ANN)": Pipeline([
        ("scaler", StandardScaler()),
        ("clf", MLPClassifier(hidden_layer_sizes=(64, 32), max_iter=1000, random_state=RANDOM_STATE))
    ]),
    "LightGBM": lgb.LGBMClassifier(
        n_estimators=300,
        learning_rate=0.1,
        num_leaves=31,
        random_state=RANDOM_STATE
    ),
    "CatBoost": CatBoostClassifier(
        iterations=300,
        learning_rate=0.1,
        depth=5,
        random_seed=RANDOM_STATE,
        verbose=0
    ),
    "Gaussian Process": Pipeline([
        ("scaler", StandardScaler()),
        ("clf", GaussianProcessClassifier(kernel=1.0 * RBF(1.0), random_state=RANDOM_STATE))
    ])
}

base_models = [
    ('rf', RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)),
    ('gb', GradientBoostingClassifier(random_state=RANDOM_STATE)),
    ('svm', Pipeline([("scaler", StandardScaler()), 
                     ("clf", SVC(probability=True, random_state=RANDOM_STATE))]))
]

models["Stacking"] = StackingClassifier(
    estimators=base_models,
    final_estimator=LogisticRegression(random_state=RANDOM_STATE),
    cv=5
)

# ---------------------------------------------------------------------
# 3. Train, predict, evaluate
# ---------------------------------------------------------------------

metric_names = ["Accuracy", "Precision", "Recall", "F1", "ROC AUC"]
metrics = {m: [] for m in metric_names}
prob_scores = {}

for name, model in models.items():
    model.fit(X_train, y_train)
    y_pred = model.predict(X_test)

    # Probabilities / decision scores
    if hasattr(model, "predict_proba"):
        y_score = model.predict_proba(X_test)
    else:
        y_dec = model.decision_function(X_test)
        # decision_function can be (n_samples,) or (n_samples, n_classes)
        if y_dec.ndim == 1:
            y_score = np.column_stack([1 - y_dec, y_dec])
        else:
            y_score = y_dec
    prob_scores[name] = y_score

    # Metrics
    metrics["Accuracy"].append(accuracy_score(y_test, y_pred))
    metrics["Precision"].append(precision_score(y_test, y_pred, average="macro", zero_division=0))
    metrics["Recall"].append(recall_score(y_test, y_pred, average="macro", zero_division=0))
    metrics["F1"].append(f1_score(y_test, y_pred, average="macro", zero_division=0))
    metrics["ROC AUC"].append(roc_auc_score(y_test, y_score, multi_class="ovr"))

# Tabulate results
results_df = pd.DataFrame(metrics, index=models.keys()).sort_values("Accuracy", ascending=False)
print("\n===== Test‑set performance =====\n")
print(results_df.to_string(float_format="{:.3f}".format))

# ---------------------------------------------------------------------
# 4. Visualisations
# ---------------------------------------------------------------------

# Bar chart of metrics
fig, ax = plt.subplots(figsize=(10, 6))
bar_width = 0.18
x = np.arange(len(models))

for i, m in enumerate(metric_names):
    ax.bar(x + (i - 2) * bar_width, results_df[m], bar_width, label=m)

ax.set_xticks(x)
ax.set_xticklabels(results_df.index, rotation=45, ha="right")
ax.set_ylabel("Score")
ax.set_title("Model Performance Metrics (Test Set)")
ax.legend()
plt.tight_layout()
plt.show()

# Confusion matrix for best model
best_model_name = results_df.index[0]
print(f"\nBest overall model: {best_model_name}")

best_model = models[best_model_name]
y_pred_best = best_model.predict(X_test)
cm = confusion_matrix(y_test, y_pred_best)

fig, ax = plt.subplots(figsize=(6, 5))
img = ax.imshow(cm, interpolation="nearest")
ax.set_title(f"Confusion Matrix – {best_model_name}")
ax.set_xlabel("Predicted label")
ax.set_ylabel("True label")

for i in range(cm.shape[0]):
    for j in range(cm.shape[1]):
        ax.text(j, i, cm[i, j], ha="center", va="center")

ax.set_xticks(np.arange(cm.shape[1]))
ax.set_yticks(np.arange(cm.shape[0]))
plt.tight_layout()
plt.show()

# ROC curves for best model (one‑vs‑rest)
fig, ax = plt.subplots(figsize=(7, 6))
for cls in np.unique(y):
    RocCurveDisplay.from_predictions(
        (y_test == cls).astype(int),
        prob_scores[best_model_name][:, cls],
        ax=ax, name=f"Class {cls} vs rest")
ax.plot([0, 1], [0, 1], linestyle="--")
ax.set_title(f"ROC Curves – {best_model_name} (One‑vs‑Rest)")
plt.tight_layout()
plt.show()

# ---------------------------------------------------------------------
# 5. Export results
# ---------------------------------------------------------------------

results_df.to_csv("model_metrics.csv", index_label="Model")
print("\nDetailed metrics written to model_metrics.csv")