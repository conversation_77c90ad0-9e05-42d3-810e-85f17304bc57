{"experiment_info": {"dataset_shape": [791, 18], "n_classes": 3, "test_size": 0.2, "cv_folds": 5, "random_state": 42}, "best_model": {"name": "LightGBM", "accuracy": 0.9685534591194969, "f1_score": 0.9501022079455567, "roc_auc": 0.9946120918908646, "training_time": 0.425659}, "top_5_models": {"LightGBM": {"Accuracy": 0.9685534591194969, "F1": 0.9501022079455567, "ROC AUC": 0.9946120918908646}, "SVM (RBF)": {"Accuracy": 0.9685534591194969, "F1": 0.9466188554727507, "ROC AUC": 0.9925533940638878}, "Stacking Classifier": {"Accuracy": 0.9685534591194969, "F1": 0.950789450616878, "ROC AUC": 0.9952902493331851}, "CatBoost": {"Accuracy": 0.9685534591194969, "F1": 0.9501022079455567, "ROC AUC": 0.9964664208579714}, "Random Forest": {"Accuracy": 0.9622641509433962, "F1": 0.9416009455248737, "ROC AUC": 0.9946004364418569}}}